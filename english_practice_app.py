#!/usr/bin/env python3
"""
English Practice App
A comprehensive English learning application with multiple question types,
progress tracking, and different difficulty levels.
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import time
import json
import os
from questions_data import (
    get_random_grammar_questions, 
    get_reading_questions, 
    get_vocabulary_questions,
    get_mixed_questions,
    DIFFICULTY_LEVELS
)

class EnglishPracticeApp:
    def __init__(self, root):
        self.root = root
        self.root.title("English Practice App")
        self.root.geometry("900x700")
        self.root.configure(bg="#f0f0f0")
        
        # Application state
        self.current_questions = []
        self.current_question_index = 0
        self.score = 0
        self.start_time = None
        self.time_limit = None
        self.user_answers = []
        self.timer_running = False
        
        # Load user progress
        self.load_progress()
        
        # Create main interface
        self.create_main_menu()
        
    def create_main_menu(self):
        """Create the main menu interface"""
        # Clear the window
        for widget in self.root.winfo_children():
            widget.destroy()
            
        # Title
        title_label = tk.Label(
            self.root, 
            text="English Practice App", 
            font=("Arial", 24, "bold"),
            bg="#f0f0f0",
            fg="#2c3e50"
        )
        title_label.pack(pady=20)
        
        # Subtitle
        subtitle_label = tk.Label(
            self.root,
            text="Improve your English skills with interactive exercises",
            font=("Arial", 12),
            bg="#f0f0f0",
            fg="#7f8c8d"
        )
        subtitle_label.pack(pady=5)
        
        # Main menu frame
        menu_frame = tk.Frame(self.root, bg="#f0f0f0")
        menu_frame.pack(pady=30)
        
        # Menu buttons
        buttons = [
            ("Grammar Practice", self.start_grammar_practice, "#3498db"),
            ("Reading Comprehension", self.start_reading_practice, "#e74c3c"),
            ("Vocabulary Builder", self.start_vocabulary_practice, "#2ecc71"),
            ("Mixed Practice", self.start_mixed_practice, "#f39c12"),
            ("View Progress", self.show_progress, "#9b59b6"),
            ("Settings", self.show_settings, "#34495e")
        ]
        
        for i, (text, command, color) in enumerate(buttons):
            btn = tk.Button(
                menu_frame,
                text=text,
                command=command,
                font=("Arial", 12, "bold"),
                bg=color,
                fg="white",
                width=20,
                height=2,
                relief="flat",
                cursor="hand2"
            )
            btn.pack(pady=8)
            
            # Hover effects
            btn.bind("<Enter>", lambda e, b=btn: b.configure(relief="raised"))
            btn.bind("<Leave>", lambda e, b=btn: b.configure(relief="flat"))
    
    def start_grammar_practice(self):
        """Start grammar practice session"""
        self.select_difficulty("grammar")
    
    def start_reading_practice(self):
        """Start reading comprehension practice"""
        self.current_questions = []
        reading_data = get_reading_questions()
        
        for passage in reading_data:
            for question in passage["questions"]:
                question["passage"] = passage["passage"]
                self.current_questions.append({"type": "reading", "data": question})
        
        if self.current_questions:
            self.start_practice_session()
        else:
            messagebox.showinfo("Info", "No reading questions available.")
    
    def start_vocabulary_practice(self):
        """Start vocabulary practice session"""
        self.select_difficulty("vocabulary")
    
    def start_mixed_practice(self):
        """Start mixed practice session"""
        self.select_difficulty("mixed")
    
    def select_difficulty(self, practice_type):
        """Show difficulty selection dialog"""
        difficulty_window = tk.Toplevel(self.root)
        difficulty_window.title("Select Difficulty")
        difficulty_window.geometry("400x300")
        difficulty_window.configure(bg="#f0f0f0")
        difficulty_window.transient(self.root)
        difficulty_window.grab_set()
        
        # Center the window
        difficulty_window.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 250,
            self.root.winfo_rooty() + 200
        ))
        
        tk.Label(
            difficulty_window,
            text="Choose Difficulty Level",
            font=("Arial", 16, "bold"),
            bg="#f0f0f0"
        ).pack(pady=20)
        
        for level, settings in DIFFICULTY_LEVELS.items():
            frame = tk.Frame(difficulty_window, bg="#f0f0f0")
            frame.pack(pady=10, padx=20, fill="x")
            
            btn = tk.Button(
                frame,
                text=f"{level.title()}",
                command=lambda l=level, pt=practice_type, w=difficulty_window: 
                    self.start_practice_with_difficulty(l, pt, w),
                font=("Arial", 12, "bold"),
                width=15,
                height=2,
                bg="#3498db",
                fg="white",
                relief="flat"
            )
            btn.pack(side="left")
            
            info_text = f"Questions: {settings['questions']}, Time: {settings['time_limit']}s"
            tk.Label(
                frame,
                text=info_text,
                font=("Arial", 10),
                bg="#f0f0f0",
                fg="#7f8c8d"
            ).pack(side="left", padx=20)
    
    def start_practice_with_difficulty(self, difficulty, practice_type, window):
        """Start practice session with selected difficulty"""
        window.destroy()
        
        settings = DIFFICULTY_LEVELS[difficulty]
        self.time_limit = settings["time_limit"]
        
        if practice_type == "grammar":
            questions = get_random_grammar_questions(settings["questions"])
            self.current_questions = [{"type": "grammar", "data": q} for q in questions]
        elif practice_type == "vocabulary":
            questions = get_vocabulary_questions(difficulty, settings["questions"])
            self.current_questions = [{"type": "vocabulary", "data": q} for q in questions]
        elif practice_type == "mixed":
            self.current_questions = get_mixed_questions(settings["questions"])
        
        if self.current_questions:
            self.start_practice_session()
        else:
            messagebox.showinfo("Info", f"No {practice_type} questions available for {difficulty} level.")
    
    def start_practice_session(self):
        """Start the practice session"""
        self.current_question_index = 0
        self.score = 0
        self.user_answers = []
        self.start_time = time.time()
        self.timer_running = True
        
        self.show_question()
    
    def show_question(self):
        """Display the current question"""
        # Clear the window
        for widget in self.root.winfo_children():
            widget.destroy()
        
        if self.current_question_index >= len(self.current_questions):
            self.show_results()
            return
        
        question_data = self.current_questions[self.current_question_index]
        question_type = question_data["type"]
        question = question_data["data"]
        
        # Header frame
        header_frame = tk.Frame(self.root, bg="#2c3e50", height=60)
        header_frame.pack(fill="x")
        header_frame.pack_propagate(False)
        
        # Progress info
        progress_text = f"Question {self.current_question_index + 1} of {len(self.current_questions)}"
        tk.Label(
            header_frame,
            text=progress_text,
            font=("Arial", 12, "bold"),
            bg="#2c3e50",
            fg="white"
        ).pack(side="left", padx=20, pady=15)
        
        # Timer
        if self.time_limit:
            elapsed = int(time.time() - self.start_time)
            remaining = max(0, self.time_limit - elapsed)
            timer_text = f"Time: {remaining//60:02d}:{remaining%60:02d}"
            self.timer_label = tk.Label(
                header_frame,
                text=timer_text,
                font=("Arial", 12, "bold"),
                bg="#2c3e50",
                fg="#e74c3c" if remaining < 30 else "white"
            )
            self.timer_label.pack(side="right", padx=20, pady=15)
            
            if remaining <= 0:
                self.show_results()
                return
        
        # Main content frame
        content_frame = tk.Frame(self.root, bg="#f0f0f0")
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        if question_type == "reading":
            self.show_reading_question(content_frame, question)
        elif question_type == "grammar":
            self.show_grammar_question(content_frame, question)
        elif question_type == "vocabulary":
            self.show_vocabulary_question(content_frame, question)
        
        # Update timer
        if self.timer_running and self.time_limit:
            self.root.after(1000, self.update_timer)
    
    def show_grammar_question(self, parent, question):
        """Display a grammar question"""
        # Question text
        question_label = tk.Label(
            parent,
            text=question["question"],
            font=("Arial", 14),
            bg="#f0f0f0",
            wraplength=800,
            justify="left"
        )
        question_label.pack(pady=20, anchor="w")
        
        # Answer options
        self.answer_var = tk.StringVar()
        options_frame = tk.Frame(parent, bg="#f0f0f0")
        options_frame.pack(pady=20, anchor="w")
        
        for i, option in enumerate(question["options"]):
            rb = tk.Radiobutton(
                options_frame,
                text=option,
                variable=self.answer_var,
                value=str(i),
                font=("Arial", 12),
                bg="#f0f0f0",
                anchor="w"
            )
            rb.pack(anchor="w", pady=5)
        
        # Submit button
        submit_btn = tk.Button(
            parent,
            text="Submit Answer",
            command=self.submit_answer,
            font=("Arial", 12, "bold"),
            bg="#27ae60",
            fg="white",
            width=15,
            height=2
        )
        submit_btn.pack(pady=20)
    
    def show_reading_question(self, parent, question):
        """Display a reading comprehension question"""
        # Passage
        passage_frame = tk.LabelFrame(parent, text="Reading Passage", font=("Arial", 12, "bold"))
        passage_frame.pack(fill="both", expand=True, pady=10)
        
        passage_text = scrolledtext.ScrolledText(
            passage_frame,
            height=10,
            font=("Arial", 11),
            wrap=tk.WORD,
            state="disabled"
        )
        passage_text.pack(fill="both", expand=True, padx=10, pady=10)
        
        passage_text.config(state="normal")
        passage_text.insert("1.0", question["passage"])
        passage_text.config(state="disabled")
        
        # Question
        question_label = tk.Label(
            parent,
            text=question["question"],
            font=("Arial", 14, "bold"),
            bg="#f0f0f0",
            wraplength=800,
            justify="left"
        )
        question_label.pack(pady=20, anchor="w")
        
        # Answer options
        self.answer_var = tk.StringVar()
        options_frame = tk.Frame(parent, bg="#f0f0f0")
        options_frame.pack(pady=10, anchor="w")
        
        for i, option in enumerate(question["options"]):
            rb = tk.Radiobutton(
                options_frame,
                text=option,
                variable=self.answer_var,
                value=str(i),
                font=("Arial", 12),
                bg="#f0f0f0",
                anchor="w"
            )
            rb.pack(anchor="w", pady=5)
        
        # Submit button
        submit_btn = tk.Button(
            parent,
            text="Submit Answer",
            command=self.submit_answer,
            font=("Arial", 12, "bold"),
            bg="#27ae60",
            fg="white",
            width=15,
            height=2
        )
        submit_btn.pack(pady=20)
    
    def show_vocabulary_question(self, parent, question):
        """Display a vocabulary question"""
        # Word
        word_label = tk.Label(
            parent,
            text=f"Word: {question['word']}",
            font=("Arial", 18, "bold"),
            bg="#f0f0f0",
            fg="#2c3e50"
        )
        word_label.pack(pady=20)
        
        # Definition
        definition_label = tk.Label(
            parent,
            text=f"Definition: {question['definition']}",
            font=("Arial", 14),
            bg="#f0f0f0",
            wraplength=700,
            justify="left"
        )
        definition_label.pack(pady=10, anchor="w")
        
        # Example
        example_label = tk.Label(
            parent,
            text=f"Example: {question['example']}",
            font=("Arial", 12, "italic"),
            bg="#f0f0f0",
            fg="#7f8c8d",
            wraplength=700,
            justify="left"
        )
        example_label.pack(pady=10, anchor="w")
        
        # Synonyms test
        tk.Label(
            parent,
            text="Which of the following is a synonym?",
            font=("Arial", 14, "bold"),
            bg="#f0f0f0"
        ).pack(pady=20, anchor="w")
        
        # Create options (correct synonyms + random words)
        import random
        options = question["synonyms"][:2]  # Take 2 correct synonyms
        wrong_options = ["incorrect", "unrelated", "opposite", "different"]
        options.extend(random.sample(wrong_options, 2))
        random.shuffle(options)
        
        self.answer_var = tk.StringVar()
        options_frame = tk.Frame(parent, bg="#f0f0f0")
        options_frame.pack(pady=10, anchor="w")
        
        for i, option in enumerate(options):
            rb = tk.Radiobutton(
                options_frame,
                text=f"({chr(65+i)}) {option}",
                variable=self.answer_var,
                value=option,
                font=("Arial", 12),
                bg="#f0f0f0",
                anchor="w"
            )
            rb.pack(anchor="w", pady=5)
        
        # Store correct answers for checking
        self.vocab_correct_answers = question["synonyms"]
        
        # Submit button
        submit_btn = tk.Button(
            parent,
            text="Submit Answer",
            command=self.submit_vocab_answer,
            font=("Arial", 12, "bold"),
            bg="#27ae60",
            fg="white",
            width=15,
            height=2
        )
        submit_btn.pack(pady=20)
    
    def submit_answer(self):
        """Submit the current answer"""
        if not hasattr(self, 'answer_var') or not self.answer_var.get():
            messagebox.showwarning("Warning", "Please select an answer.")
            return
        
        question_data = self.current_questions[self.current_question_index]
        question = question_data["data"]
        user_answer = int(self.answer_var.get())
        correct_answer = question["correct"]
        
        is_correct = user_answer == correct_answer
        if is_correct:
            self.score += 1
        
        self.user_answers.append({
            "question": question,
            "user_answer": user_answer,
            "correct_answer": correct_answer,
            "is_correct": is_correct,
            "type": question_data["type"]
        })
        
        self.current_question_index += 1
        self.show_question()
    
    def submit_vocab_answer(self):
        """Submit vocabulary answer"""
        if not hasattr(self, 'answer_var') or not self.answer_var.get():
            messagebox.showwarning("Warning", "Please select an answer.")
            return
        
        user_answer = self.answer_var.get()
        is_correct = user_answer in self.vocab_correct_answers
        
        if is_correct:
            self.score += 1
        
        question_data = self.current_questions[self.current_question_index]
        self.user_answers.append({
            "question": question_data["data"],
            "user_answer": user_answer,
            "correct_answer": self.vocab_correct_answers,
            "is_correct": is_correct,
            "type": "vocabulary"
        })
        
        self.current_question_index += 1
        self.show_question()
    
    def update_timer(self):
        """Update the timer display"""
        if not self.timer_running or not self.time_limit:
            return
        
        elapsed = int(time.time() - self.start_time)
        remaining = max(0, self.time_limit - elapsed)
        
        if hasattr(self, 'timer_label'):
            timer_text = f"Time: {remaining//60:02d}:{remaining%60:02d}"
            self.timer_label.config(
                text=timer_text,
                fg="#e74c3c" if remaining < 30 else "white"
            )
        
        if remaining <= 0:
            self.timer_running = False
            self.show_results()
        else:
            self.root.after(1000, self.update_timer)
    
    def show_results(self):
        """Show practice session results"""
        self.timer_running = False
        
        # Clear the window
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # Results header
        header_frame = tk.Frame(self.root, bg="#2c3e50", height=80)
        header_frame.pack(fill="x")
        header_frame.pack_propagate(False)
        
        tk.Label(
            header_frame,
            text="Practice Session Results",
            font=("Arial", 20, "bold"),
            bg="#2c3e50",
            fg="white"
        ).pack(pady=25)
        
        # Results content
        content_frame = tk.Frame(self.root, bg="#f0f0f0")
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Score summary
        total_questions = len(self.current_questions)
        percentage = (self.score / total_questions * 100) if total_questions > 0 else 0
        
        score_frame = tk.Frame(content_frame, bg="#ecf0f1", relief="raised", bd=2)
        score_frame.pack(fill="x", pady=20)
        
        tk.Label(
            score_frame,
            text=f"Score: {self.score}/{total_questions} ({percentage:.1f}%)",
            font=("Arial", 18, "bold"),
            bg="#ecf0f1",
            fg="#2c3e50"
        ).pack(pady=15)
        
        # Time taken
        if self.start_time:
            time_taken = int(time.time() - self.start_time)
            tk.Label(
                score_frame,
                text=f"Time taken: {time_taken//60:02d}:{time_taken%60:02d}",
                font=("Arial", 14),
                bg="#ecf0f1",
                fg="#7f8c8d"
            ).pack(pady=5)
        
        # Performance message
        if percentage >= 90:
            message = "Excellent work! 🌟"
            color = "#27ae60"
        elif percentage >= 70:
            message = "Good job! 👍"
            color = "#f39c12"
        elif percentage >= 50:
            message = "Keep practicing! 📚"
            color = "#e67e22"
        else:
            message = "More practice needed! 💪"
            color = "#e74c3c"
        
        tk.Label(
            score_frame,
            text=message,
            font=("Arial", 16, "bold"),
            bg="#ecf0f1",
            fg=color
        ).pack(pady=10)
        
        # Buttons
        button_frame = tk.Frame(content_frame, bg="#f0f0f0")
        button_frame.pack(pady=20)
        
        tk.Button(
            button_frame,
            text="Review Answers",
            command=self.show_review,
            font=("Arial", 12, "bold"),
            bg="#3498db",
            fg="white",
            width=15,
            height=2
        ).pack(side="left", padx=10)
        
        tk.Button(
            button_frame,
            text="Back to Menu",
            command=self.create_main_menu,
            font=("Arial", 12, "bold"),
            bg="#95a5a6",
            fg="white",
            width=15,
            height=2
        ).pack(side="left", padx=10)
        
        # Save progress
        self.save_progress()
    
    def show_review(self):
        """Show detailed review of answers"""
        review_window = tk.Toplevel(self.root)
        review_window.title("Answer Review")
        review_window.geometry("800x600")
        review_window.configure(bg="#f0f0f0")
        
        # Create scrollable frame
        canvas = tk.Canvas(review_window, bg="#f0f0f0")
        scrollbar = ttk.Scrollbar(review_window, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f0f0f0")
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Header
        tk.Label(
            scrollable_frame,
            text="Answer Review",
            font=("Arial", 18, "bold"),
            bg="#f0f0f0",
            fg="#2c3e50"
        ).pack(pady=20)
        
        # Review each answer
        for i, answer_data in enumerate(self.user_answers):
            frame = tk.Frame(scrollable_frame, bg="white", relief="raised", bd=1)
            frame.pack(fill="x", padx=20, pady=10)
            
            # Question number and status
            status_color = "#27ae60" if answer_data["is_correct"] else "#e74c3c"
            status_text = "✓ Correct" if answer_data["is_correct"] else "✗ Incorrect"
            
            tk.Label(
                frame,
                text=f"Question {i+1}: {status_text}",
                font=("Arial", 12, "bold"),
                bg="white",
                fg=status_color
            ).pack(anchor="w", padx=10, pady=5)
            
            # Question text
            if answer_data["type"] == "vocabulary":
                question_text = f"Word: {answer_data['question']['word']}"
            else:
                question_text = answer_data["question"]["question"]
            
            tk.Label(
                frame,
                text=question_text,
                font=("Arial", 11),
                bg="white",
                wraplength=700,
                justify="left"
            ).pack(anchor="w", padx=10, pady=2)
            
            # Show answers
            if answer_data["type"] == "vocabulary":
                tk.Label(
                    frame,
                    text=f"Your answer: {answer_data['user_answer']}",
                    font=("Arial", 10),
                    bg="white",
                    fg="#7f8c8d"
                ).pack(anchor="w", padx=10, pady=1)
                
                tk.Label(
                    frame,
                    text=f"Correct answers: {', '.join(answer_data['correct_answer'])}",
                    font=("Arial", 10),
                    bg="white",
                    fg="#27ae60"
                ).pack(anchor="w", padx=10, pady=1)
            else:
                options = answer_data["question"]["options"]
                user_choice = options[answer_data["user_answer"]] if answer_data["user_answer"] < len(options) else "No answer"
                correct_choice = options[answer_data["correct_answer"]]
                
                tk.Label(
                    frame,
                    text=f"Your answer: {user_choice}",
                    font=("Arial", 10),
                    bg="white",
                    fg="#7f8c8d"
                ).pack(anchor="w", padx=10, pady=1)
                
                tk.Label(
                    frame,
                    text=f"Correct answer: {correct_choice}",
                    font=("Arial", 10),
                    bg="white",
                    fg="#27ae60"
                ).pack(anchor="w", padx=10, pady=1)
            
            # Explanation
            if "explanation" in answer_data["question"]:
                tk.Label(
                    frame,
                    text=f"Explanation: {answer_data['question']['explanation']}",
                    font=("Arial", 10, "italic"),
                    bg="white",
                    fg="#34495e",
                    wraplength=700,
                    justify="left"
                ).pack(anchor="w", padx=10, pady=5)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def show_progress(self):
        """Show user progress and statistics"""
        messagebox.showinfo("Progress", "Progress tracking feature coming soon!")
    
    def show_settings(self):
        """Show application settings"""
        messagebox.showinfo("Settings", "Settings feature coming soon!")
    
    def load_progress(self):
        """Load user progress from file"""
        try:
            if os.path.exists("progress.json"):
                with open("progress.json", "r") as f:
                    self.progress_data = json.load(f)
            else:
                self.progress_data = {"sessions": [], "total_score": 0, "total_questions": 0}
        except:
            self.progress_data = {"sessions": [], "total_score": 0, "total_questions": 0}
    
    def save_progress(self):
        """Save user progress to file"""
        try:
            session_data = {
                "date": time.strftime("%Y-%m-%d %H:%M:%S"),
                "score": self.score,
                "total_questions": len(self.current_questions),
                "percentage": (self.score / len(self.current_questions) * 100) if self.current_questions else 0
            }
            
            self.progress_data["sessions"].append(session_data)
            self.progress_data["total_score"] += self.score
            self.progress_data["total_questions"] += len(self.current_questions)
            
            with open("progress.json", "w") as f:
                json.dump(self.progress_data, f, indent=2)
        except Exception as e:
            print(f"Error saving progress: {e}")

def main():
    """Main function to run the application"""
    root = tk.Tk()
    app = EnglishPracticeApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Test script for English Practice App
Verifies that all components are working correctly
"""

import sys
import os

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import tkinter as tk
        print("✓ tkinter imported successfully")
    except ImportError:
        print("✗ tkinter import failed")
        return False
    
    try:
        from questions_data import (
            get_random_grammar_questions,
            get_reading_questions,
            get_vocabulary_questions,
            get_mixed_questions,
            DIFFICULTY_LEVELS
        )
        print("✓ questions_data imported successfully")
    except ImportError as e:
        print(f"✗ questions_data import failed: {e}")
        return False
    
    return True

def test_question_data():
    """Test if question data is properly formatted"""
    print("\nTesting question data...")
    
    try:
        from questions_data import (
            GRAMMAR_QUESTIONS,
            READING_QUESTIONS,
            VOCABULARY_QUESTIONS,
            get_random_grammar_questions,
            get_reading_questions,
            get_vocabulary_questions
        )
        
        # Test grammar questions
        if len(GRAMMAR_QUESTIONS) > 0:
            print(f"✓ {len(GRAMMAR_QUESTIONS)} grammar questions loaded")
            
            # Check format of first grammar question
            q = GRAMMAR_QUESTIONS[0]
            required_keys = ["question", "options", "correct", "explanation"]
            if all(key in q for key in required_keys):
                print("✓ Grammar question format is correct")
            else:
                print("✗ Grammar question format is incorrect")
                return False
        else:
            print("✗ No grammar questions found")
            return False
        
        # Test reading questions
        if len(READING_QUESTIONS) > 0:
            print(f"✓ {len(READING_QUESTIONS)} reading passages loaded")
            
            # Check format of first reading question
            passage = READING_QUESTIONS[0]
            if "passage" in passage and "questions" in passage:
                print("✓ Reading question format is correct")
            else:
                print("✗ Reading question format is incorrect")
                return False
        else:
            print("✗ No reading questions found")
            return False
        
        # Test vocabulary questions
        if len(VOCABULARY_QUESTIONS) > 0:
            print(f"✓ {len(VOCABULARY_QUESTIONS)} vocabulary questions loaded")
            
            # Check format of first vocabulary question
            v = VOCABULARY_QUESTIONS[0]
            required_keys = ["word", "definition", "example", "synonyms", "difficulty"]
            if all(key in v for key in required_keys):
                print("✓ Vocabulary question format is correct")
            else:
                print("✗ Vocabulary question format is incorrect")
                return False
        else:
            print("✗ No vocabulary questions found")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing question data: {e}")
        return False

def test_question_functions():
    """Test question retrieval functions"""
    print("\nTesting question functions...")
    
    try:
        from questions_data import (
            get_random_grammar_questions,
            get_reading_questions,
            get_vocabulary_questions,
            get_mixed_questions
        )
        
        # Test grammar function
        grammar_q = get_random_grammar_questions(5)
        if len(grammar_q) > 0:
            print(f"✓ get_random_grammar_questions returned {len(grammar_q)} questions")
        else:
            print("✗ get_random_grammar_questions returned no questions")
            return False
        
        # Test reading function
        reading_q = get_reading_questions()
        if len(reading_q) > 0:
            print(f"✓ get_reading_questions returned {len(reading_q)} passages")
        else:
            print("✗ get_reading_questions returned no passages")
            return False
        
        # Test vocabulary function
        vocab_q = get_vocabulary_questions(count=3)
        if len(vocab_q) > 0:
            print(f"✓ get_vocabulary_questions returned {len(vocab_q)} questions")
        else:
            print("✗ get_vocabulary_questions returned no questions")
            return False
        
        # Test mixed function
        mixed_q = get_mixed_questions(10)
        if len(mixed_q) > 0:
            print(f"✓ get_mixed_questions returned {len(mixed_q)} questions")
        else:
            print("✗ get_mixed_questions returned no questions")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing question functions: {e}")
        return False

def test_difficulty_levels():
    """Test difficulty level configuration"""
    print("\nTesting difficulty levels...")
    
    try:
        from questions_data import DIFFICULTY_LEVELS
        
        expected_levels = ["beginner", "intermediate", "advanced"]
        
        for level in expected_levels:
            if level in DIFFICULTY_LEVELS:
                settings = DIFFICULTY_LEVELS[level]
                if "time_limit" in settings and "questions" in settings:
                    print(f"✓ {level} level configured correctly")
                else:
                    print(f"✗ {level} level missing required settings")
                    return False
            else:
                print(f"✗ {level} level not found")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing difficulty levels: {e}")
        return False

def test_app_creation():
    """Test if the main app can be created"""
    print("\nTesting app creation...")
    
    try:
        import tkinter as tk
        
        # Create a test root window
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        # Try to import the main app class
        from english_practice_app import EnglishPracticeApp
        
        # Create app instance
        app = EnglishPracticeApp(root)
        
        print("✓ EnglishPracticeApp created successfully")
        
        # Clean up
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ Error creating app: {e}")
        return False

def test_file_structure():
    """Test if all required files exist"""
    print("\nTesting file structure...")
    
    required_files = [
        "english_practice_app.py",
        "questions_data.py",
        "README.md"
    ]
    
    all_exist = True
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file} exists")
        else:
            print(f"✗ {file} missing")
            all_exist = False
    
    return all_exist

def run_all_tests():
    """Run all tests"""
    print("=" * 50)
    print("English Practice App - Test Suite")
    print("=" * 50)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Imports", test_imports),
        ("Question Data", test_question_data),
        ("Question Functions", test_question_functions),
        ("Difficulty Levels", test_difficulty_levels),
        ("App Creation", test_app_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 30}")
        print(f"Running {test_name} Test")
        print(f"{'-' * 30}")
        
        if test_func():
            print(f"✓ {test_name} test PASSED")
            passed += 1
        else:
            print(f"✗ {test_name} test FAILED")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    print("=" * 50)
    
    if passed == total:
        print("🎉 All tests passed! The app is ready to use.")
        print("\nTo run the app:")
        print("python3 english_practice_app.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False
    
    return True

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)

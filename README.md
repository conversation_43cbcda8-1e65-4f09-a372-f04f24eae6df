# English Practice App

A comprehensive English learning application built with Python and Tkinter GUI, designed to help users improve their English skills through interactive exercises.

## Features

### 🎯 Multiple Practice Modes
- **Grammar Practice**: TOEIC-style grammar questions with multiple choice answers
- **Reading Comprehension**: Text passages with comprehension questions
- **Vocabulary Builder**: Word definitions, examples, and synonym exercises
- **Mixed Practice**: Combination of all question types

### 📊 Difficulty Levels
- **Beginner**: 10 questions, 120 seconds
- **Intermediate**: 15 questions, 90 seconds  
- **Advanced**: 20 questions, 60 seconds

### ⏱️ Features
- **Timer**: Timed practice sessions with countdown
- **Progress Tracking**: Score tracking and session history
- **Answer Review**: Detailed review with explanations
- **User-Friendly Interface**: Clean, modern GUI design

## Installation

### Prerequisites
- Python 3.6 or higher
- tkinter (usually included with Python)

### Setup
1. Clone or download the project files
2. Ensure you have both files in the same directory:
   - `english_practice_app.py` (main application)
   - `questions_data.py` (question database)

## Usage

### Running the Application
```bash
python3 english_practice_app.py
```

### Main Menu Options
1. **Grammar Practice**: Practice grammar rules and sentence structure
2. **Reading Comprehension**: Read passages and answer questions
3. **Vocabulary Builder**: Learn new words and their meanings
4. **Mixed Practice**: Combined practice with all question types
5. **View Progress**: See your learning statistics (coming soon)
6. **Settings**: Application settings (coming soon)

### Practice Session Flow
1. Select a practice mode from the main menu
2. Choose your difficulty level
3. Answer questions within the time limit
4. Review your results and explanations
5. Track your progress over time

## Question Types

### Grammar Questions
Based on TOEIC Part 5 format:
- Fill-in-the-blank sentences
- Multiple choice options (A, B, C, D)
- Focus on grammar rules, vocabulary, and sentence structure

### Reading Comprehension
Based on TOEIC Part 7 format:
- Business-related passages
- Multiple questions per passage
- Tests reading comprehension and inference skills

### Vocabulary Questions
- Word definitions and examples
- Synonym identification
- Context-based usage

## Sample Questions

The app includes questions covering:
- Prepositions and conjunctions
- Verb tenses and forms
- Adjectives and adverbs
- Business vocabulary
- Reading comprehension passages
- Word synonyms and definitions

## Progress Tracking

The application automatically saves your progress in `progress.json`, including:
- Session dates and times
- Scores and percentages
- Total questions answered
- Performance trends

## File Structure

```
english_practice_app/
├── english_practice_app.py    # Main application file
├── questions_data.py          # Question database
├── README.md                  # This file
└── progress.json             # Auto-generated progress file
```

## Customization

### Adding New Questions

To add new questions, edit `questions_data.py`:

#### Grammar Questions
```python
{
    "question": "Your question text with ------- blank.",
    "options": ["(A) option1", "(B) option2", "(C) option3", "(D) option4"],
    "correct": 1,  # Index of correct answer (0-3)
    "explanation": "Explanation of the correct answer."
}
```

#### Vocabulary Questions
```python
{
    "word": "Word",
    "definition": "Definition of the word",
    "example": "Example sentence using the word.",
    "synonyms": ["synonym1", "synonym2", "synonym3"],
    "difficulty": "beginner|intermediate|advanced"
}
```

#### Reading Comprehension
```python
{
    "passage": "Your reading passage text...",
    "questions": [
        {
            "question": "Question about the passage?",
            "options": ["(A) option1", "(B) option2", "(C) option3", "(D) option4"],
            "correct": 0,
            "explanation": "Why this answer is correct."
        }
    ]
}
```

### Modifying Difficulty Settings

Edit the `DIFFICULTY_LEVELS` dictionary in `questions_data.py`:
```python
DIFFICULTY_LEVELS = {
    "beginner": {"time_limit": 120, "questions": 10},
    "intermediate": {"time_limit": 90, "questions": 15},
    "advanced": {"time_limit": 60, "questions": 20}
}
```

## Technical Details

### Dependencies
- **tkinter**: GUI framework (built into Python)
- **json**: Progress data storage
- **time**: Timer functionality
- **random**: Question randomization
- **os**: File operations

### Key Classes and Methods
- `EnglishPracticeApp`: Main application class
- `create_main_menu()`: Creates the main interface
- `show_question()`: Displays current question
- `submit_answer()`: Processes user answers
- `show_results()`: Shows session results
- `show_review()`: Detailed answer review

## Future Enhancements

Planned features for future versions:
- [ ] Advanced progress analytics
- [ ] User profiles and accounts
- [ ] More question types (listening, writing)
- [ ] Adaptive difficulty based on performance
- [ ] Export progress reports
- [ ] Custom study plans
- [ ] Audio pronunciation guides
- [ ] Multiplayer challenges

## Troubleshooting

### Common Issues

1. **Application won't start**
   - Ensure Python 3.6+ is installed
   - Check that tkinter is available: `python3 -c "import tkinter"`

2. **Questions not loading**
   - Verify `questions_data.py` is in the same directory
   - Check for syntax errors in the questions file

3. **Progress not saving**
   - Ensure write permissions in the application directory
   - Check available disk space

### Error Messages
- "No questions available": Check question database files
- "Please select an answer": Choose an option before submitting
- Timer issues: Restart the application

## Contributing

To contribute to this project:
1. Add new questions to the database
2. Report bugs or suggest features
3. Improve the user interface
4. Add new question types or practice modes

## License

This project is open source and available for educational use.

## Support

For questions or issues:
- Check the troubleshooting section
- Review the code comments for technical details
- Test with the sample questions provided

---

**Happy Learning! 📚✨**

Improve your English skills one question at a time with this interactive practice application.

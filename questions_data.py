# English Practice App - Questions Database
# Based on TOEIC-style questions from the test files

import random

# Grammar Questions (Part 5 style)
GRAMMAR_QUESTIONS = [
    {
        "question": "The Technical Department is currently formulating written guidelines ------- the use of our micro-publishing facilities.",
        "options": ["(A) in", "(B) for", "(C) at", "(D) with"],
        "correct": 1,  # B
        "explanation": "Use 'for' to indicate purpose or intended use."
    },
    {
        "question": "Company strategists -------- predicted that conditions in the Middle East would eventually stabilize.",
        "options": ["(A) wrong", "(B) wronged", "(C) wrongly", "(D) wrongness"],
        "correct": 2,  # C
        "explanation": "Use the adverb 'wrongly' to modify the verb 'predicted'."
    },
    {
        "question": "As you would expect at one of the world's most reputable hotels, the -------- is prompt, efficient, and discreet.",
        "options": ["(A) exertion", "(B) decision", "(C) challenge", "(D) service"],
        "correct": 3,  # D
        "explanation": "In the context of hotels, 'service' is the most appropriate noun."
    },
    {
        "question": "The director ------- has often been seen to take his lunch in the staff canteen.",
        "options": ["(A) him", "(B) his", "(C) himself", "(D) he"],
        "correct": 2,  # C
        "explanation": "Use the reflexive pronoun 'himself' to emphasize the director personally."
    },
    {
        "question": "The new head of marketing is already making his mark even though he only ------- 6 weeks ago.",
        "options": ["(A) carried", "(B) elapsed", "(C) deliberated", "(D) arrived"],
        "correct": 3,  # D
        "explanation": "'Arrived' is the correct verb for someone starting a new position."
    },
    {
        "question": "This is an excellent time to consider changing jobs because of the large number of positions ------- available.",
        "options": ["(A) commonly", "(B) currently", "(C) actively", "(D) approvingly"],
        "correct": 1,  # B
        "explanation": "'Currently' means 'at the present time', which fits the context."
    },
    {
        "question": "------- leaving school, she has worked in a variety of positions but has not yet found one which suits her.",
        "options": ["(A) Despite", "(B) Since", "(C) In spite of", "(D) If"],
        "correct": 1,  # B
        "explanation": "'Since' indicates a time period from leaving school until now."
    },
    {
        "question": "Although he did not perform -------- well as a student, he became one of the most respected scholars.",
        "options": ["(A) especially", "(B) sufficiently", "(C) desperately", "(D) excellently"],
        "correct": 0,  # A
        "explanation": "'Especially' means 'particularly' and fits the context of comparison."
    },
    {
        "question": "The chief financial officer believes that we should maintain the present course, and ------- his deputy.",
        "options": ["(A) as to", "(B) whereas", "(C) as does", "(D) as long as"],
        "correct": 2,  # C
        "explanation": "'As does' is used to show agreement between two people."
    },
    {
        "question": "The pharmaceutical company insists that its new drug is ------- safe when used under supervision.",
        "options": ["(A) perfect", "(B) perfection", "(C) perfectly", "(D) perfecting"],
        "correct": 2,  # C
        "explanation": "Use the adverb 'perfectly' to modify the adjective 'safe'."
    }
]

# Reading Comprehension Questions
READING_QUESTIONS = [
    {
        "passage": """Joy-Market Returns Policy

With a few exceptions, anything purchased from a Joy Market store may be exchanged or returned for a full refund of the purchase price within 30 days provided that:
- the goods are unused
- the goods are placed in their original package
- proof of purchase is provided

IMPORTANT: Food purchased from our fresh food counters must be returned no later than 24 hours after purchase.

Some items must be returned unopened: toys, music CDs and DVDs, computer software and hardware, videos, glassware, kitchenware, undergarments, and packaged hardware items.

Customers returning Joy-Market items received as gifts should provide photo identification.

For returns of goods valued at less than $100, an exchange certificate or money order will be issued on the spot. For returns of goods valued at $100 or more, a check will be mailed within 3 working days.""",
        "questions": [
            {
                "question": "What is NOT stated in the policy?",
                "options": [
                    "(A) Goods must be brought back in their original form",
                    "(B) The original receipt must accompany the returned item",
                    "(C) A time limit is placed on items being returned",
                    "(D) A full refund will be provided for all items if returned within 24 hours"
                ],
                "correct": 3,
                "explanation": "The policy doesn't state that ALL items get full refunds within 24 hours - only fresh food has a 24-hour limit."
            },
            {
                "question": "When would a person have to show photo identification?",
                "options": [
                    "(A) When returning items valued over $100",
                    "(B) If the merchandise was originally bought by someone else",
                    "(C) When boxes have been opened and used",
                    "(D) If a credit card was used for purchase"
                ],
                "correct": 1,
                "explanation": "Photo ID is required when returning items received as gifts (bought by someone else)."
            },
            {
                "question": "How long will it take to get a refund for a purchase under $100?",
                "options": [
                    "(A) It depends on the product purchased",
                    "(B) A refund will be provided right away",
                    "(C) At least 3 business days",
                    "(D) 24 hours if receipt is provided"
                ],
                "correct": 1,
                "explanation": "For items under $100, exchange certificates or money orders are issued 'on the spot'."
            }
        ]
    }
]

# Vocabulary Questions
VOCABULARY_QUESTIONS = [
    {
        "word": "Formulating",
        "definition": "Creating or developing systematically",
        "example": "The team is formulating a new strategy.",
        "synonyms": ["developing", "creating", "devising"],
        "difficulty": "intermediate"
    },
    {
        "word": "Reputable",
        "definition": "Having a good reputation; respected",
        "example": "She works for a reputable law firm.",
        "synonyms": ["respected", "esteemed", "prestigious"],
        "difficulty": "intermediate"
    },
    {
        "word": "Discreet",
        "definition": "Careful not to attract attention; tactful",
        "example": "The waiter was discreet about the celebrity's presence.",
        "synonyms": ["tactful", "diplomatic", "subtle"],
        "difficulty": "advanced"
    },
    {
        "word": "Stabilize",
        "definition": "To make or become stable",
        "example": "The medication helped stabilize his condition.",
        "synonyms": ["steady", "balance", "secure"],
        "difficulty": "intermediate"
    },
    {
        "word": "Efficient",
        "definition": "Working in a well-organized way",
        "example": "The new system is more efficient than the old one.",
        "synonyms": ["effective", "productive", "streamlined"],
        "difficulty": "beginner"
    }
]

# Difficulty levels
DIFFICULTY_LEVELS = {
    "beginner": {"time_limit": 120, "questions": 10},
    "intermediate": {"time_limit": 90, "questions": 15},
    "advanced": {"time_limit": 60, "questions": 20}
}

def get_random_grammar_questions(count=10):
    """Get random grammar questions"""
    return random.sample(GRAMMAR_QUESTIONS, min(count, len(GRAMMAR_QUESTIONS)))

def get_reading_questions():
    """Get reading comprehension questions"""
    return READING_QUESTIONS

def get_vocabulary_questions(difficulty=None, count=5):
    """Get vocabulary questions by difficulty"""
    if difficulty:
        filtered = [q for q in VOCABULARY_QUESTIONS if q["difficulty"] == difficulty]
        return random.sample(filtered, min(count, len(filtered)))
    return random.sample(VOCABULARY_QUESTIONS, min(count, len(VOCABULARY_QUESTIONS)))

def get_mixed_questions(count=15):
    """Get a mix of different question types"""
    grammar = get_random_grammar_questions(count // 2)
    vocab = get_vocabulary_questions(count=count // 3)
    reading = get_reading_questions()
    
    mixed = []
    for q in grammar:
        mixed.append({"type": "grammar", "data": q})
    
    for q in vocab:
        mixed.append({"type": "vocabulary", "data": q})
    
    for passage in reading:
        for q in passage["questions"]:
            q["passage"] = passage["passage"]
            mixed.append({"type": "reading", "data": q})
    
    return random.sample(mixed, min(count, len(mixed)))
